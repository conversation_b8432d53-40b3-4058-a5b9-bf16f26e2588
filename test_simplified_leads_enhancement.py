#!/usr/bin/env python3
"""
Test script to verify the simplified Leads-only datacenter switching enhancement.
"""

import sys
import os

def check_simplified_implementation():
    """Check that the simplified implementation is correctly set up"""
    print("=== Checking Simplified Leads-Only Enhancement ===")
    
    # Check server.py for the conditional logic
    with open("src/server.py", "r") as f:
        server_content = f.read()
    
    # Check zoho_api.py for the new methods
    with open("src/zoho_api.py", "r") as f:
        api_content = f.read()
    
    print("\n--- Server Implementation ---")
    
    # Check for conditional logic in server
    if 'if module == "Leads":' in server_content:
        print("✓ Conditional logic for Leads found in server")
    else:
        print("✗ No conditional logic for Leads in server")
    
    if "search_by_phone_with_dc_switching" in server_content:
        print("✓ Enhanced method call for Leads found")
    else:
        print("✗ Enhanced method call for Leads not found")
    
    if "search_by_phone(" in server_content and "else:" in server_content:
        print("✓ Simple method call for other modules found")
    else:
        print("✗ Simple method call for other modules not found")
    
    print("\n--- API Implementation ---")
    
    # Check for the two different methods
    if "search_by_phone_with_dc_switching" in api_content:
        print("✓ Enhanced DC switching method found")
    else:
        print("✗ Enhanced DC switching method not found")
    
    if "simple version" in api_content:
        print("✓ Simple search method found")
    else:
        print("✗ Simple search method not found")
    
    if "for Leads only" in api_content:
        print("✓ Leads-only documentation found")
    else:
        print("✗ Leads-only documentation not found")
    
    # Check that multi_call is simplified
    if "Simple multi-datacenter call without complex token refresh logic" in api_content:
        print("✓ Simplified multi_call method found")
    else:
        print("✗ Multi_call method not simplified")

def analyze_complexity_reduction():
    """Analyze how complexity has been reduced"""
    print("\n=== Complexity Reduction Analysis ===")
    
    with open("src/zoho_api.py", "r") as f:
        content = f.read()
    
    # Count occurrences of complex logic
    complex_features = [
        ("_successful_datacenter", "Successful datacenter tracking"),
        ("_get_prioritized_datacenters", "Datacenter prioritization"),
        ("_handle_auth_failure", "Centralized auth failure handling"),
        ("_reset_token_refresh_state", "Token refresh state reset"),
    ]
    
    print("\n--- Complex Features (kept for Leads only) ---")
    for feature, description in complex_features:
        count = content.count(feature)
        print(f"  {description}: {count} occurrences")
    
    # Check that simple multi_call doesn't use complex features
    multi_call_start = content.find("async def multi_call(")
    multi_call_end = content.find("async def create_record(", multi_call_start)
    multi_call_content = content[multi_call_start:multi_call_end]
    
    print("\n--- Multi_call Simplification ---")
    if "_reset_token_refresh_state" not in multi_call_content:
        print("✓ Multi_call doesn't reset token refresh state")
    else:
        print("✗ Multi_call still resets token refresh state")
    
    if "_get_prioritized_datacenters" not in multi_call_content:
        print("✓ Multi_call uses simple datacenter ordering")
    else:
        print("✗ Multi_call still uses complex prioritization")

def main():
    """Main test function"""
    print("=== Testing Simplified Leads-Only Enhancement ===")
    
    check_simplified_implementation()
    analyze_complexity_reduction()
    
    print("\n=== Expected Behavior ===")
    print("1. Leads search: Uses enhanced DC switching with token refresh")
    print("2. Contacts search: Uses simple multi_call")
    print("3. Accounts search: Uses simple multi_call")
    print("4. Deals search: Uses simple multi_call")
    
    print("\n=== Benefits of Simplification ===")
    print("• Reduced complexity for 75% of modules (Contacts, Accounts, Deals)")
    print("• Enhanced functionality only where most needed (Leads)")
    print("• Easier to maintain and debug")
    print("• Lower risk of introducing bugs in other modules")
    print("• Clear separation of concerns")
    
    print("\n=== Log Output Expectations ===")
    print("Leads search:")
    print("  'Searching Leads across 6 datacenters with DC switching'")
    print("  'Starting centralized token refresh from [DC]'")
    print("  'Prioritizing [DC] datacenter (successful token refresh)'")
    print("")
    print("Other modules:")
    print("  'Trying 6 datacenters in priority order'")
    print("  'SUCCESS from [DC] - stopping other datacenter attempts'")
    
    print("\n✓ Simplified Leads-only enhancement implementation verified!")

if __name__ == "__main__":
    main()
