#!/usr/bin/env python3
"""
Zoho CRM MCP Server Package
A modular, production-ready MCP server for Zoho CRM integration.
"""

__version__ = "1.0.0"
__author__ = "Zoho CRM MCP Server"
__description__ = "Model Context Protocol server for Zoho CRM REST API integration"

from .config import get_settings, Settings
from .server import server, create_app, start_server, main
from .auth import AuthHandler
from .zoho_api import MultiDatacenterZohoClient

__all__ = [
    "get_settings",
    "Settings",
    "server",
    "create_app",
    "start_server",
    "main",
    "AuthHandler",
    "MultiDatacenterZohoClient",
]
