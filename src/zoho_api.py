#!/usr/bin/env python3
"""
Zoho CRM API client module - Simplified version with datacenter-specific token refresh
Handles Zoho CRM REST API interactions with proper token refresh and multi-datacenter support.
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any

import httpx
from .config import ZohoConfig
from .zoho_oauth import get_zoho_provider

logger = logging.getLogger(__name__)


class ZohoAPIError(Exception):
    """Custom exception for Zoho API errors"""

    def __init__(self, message: str, status_code: Optional[int] = None):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)


class MultiDatacenterZohoClient:
    """Simplified multi-datacenter Zoho API client with datacenter-specific token refresh"""

    def __init__(self, base_config: ZohoConfig):
        self.base_config = base_config
        self.datacenters = ["US", "IN"]
        self.timeout = base_config.timeout

        logger.info(
            f"Multi-DC Zoho client initialized for {len(self.datacenters)} datacenters"
        )

    def _get_prioritized_datacenters(self) -> List[str]:
        """Get datacenters with primary datacenter first"""
        primary_dc = self.base_config.environment
        other_dcs = [dc for dc in self.datacenters if dc != primary_dc]
        return [primary_dc] + other_dcs

    def _get_datacenter_url(self, datacenter: str, endpoint: str) -> str:
        """Get the full API URL for a specific datacenter"""
        env_urls = {
            "US": "https://www.zohoapis.com",
            "IN": "https://www.zohoapis.in",
        }
        base_url = env_urls.get(datacenter, env_urls["US"])
        return f"{base_url}/crm/{self.base_config.api_version}/{endpoint.lstrip('/')}"

    async def _make_request_with_refresh(
        self,
        method: str,
        url: str,
        datacenter: str,
        access_token: str,
        refresh_token: Optional[str] = None,
        data: Optional[Dict] = None,
        params: Optional[Dict] = None,
    ) -> Dict[str, Any]:
        """Make HTTP request with automatic token refresh using the same datacenter's OAuth endpoint"""

        headers = {
            "Authorization": f"Zoho-oauthtoken {access_token}",
            "Content-Type": "application/json",
        }

        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                # Make the initial request
                if method.upper() == "GET":
                    response = await client.get(url, headers=headers, params=params)
                elif method.upper() == "POST":
                    response = await client.post(
                        url, headers=headers, json=data, params=params
                    )
                else:
                    raise ZohoAPIError(f"Unsupported HTTP method: {method}")

                # Log the full response for debugging
                logger.info(
                    f"API Response from {datacenter}: Status {response.status_code}"
                )
                logger.info(f"Response Headers: {dict(response.headers)}")
                logger.info(f"Response Text: {response.text}")

                # Handle successful responses
                if response.status_code in [200, 201, 202]:
                    try:
                        response_data = response.json()
                        logger.info(
                            f"Parsed JSON Response: {json.dumps(response_data, indent=2)}"
                        )
                        return {
                            "success": True,
                            "data": response_data,
                            "status_code": response.status_code,
                        }
                    except json.JSONDecodeError:
                        logger.warning(
                            f"Failed to parse JSON response: {response.text}"
                        )
                        return {
                            "success": True,
                            "data": {"raw_response": response.text},
                            "status_code": response.status_code,
                        }

                # Handle 401 errors with datacenter-specific token refresh
                elif response.status_code == 401 and refresh_token:
                    logger.warning(
                        f"Access token expired for {datacenter}, attempting refresh using {datacenter} OAuth endpoint..."
                    )

                    # Create datacenter-specific config for token refresh
                    dc_config = ZohoConfig(
                        environment=datacenter,
                        environment_type=self.base_config.environment_type,
                        api_version=self.base_config.api_version,
                        timeout=self.base_config.timeout,
                        client_id=self.base_config.client_id,
                        client_secret=self.base_config.client_secret,
                    )

                    # Use datacenter-specific OAuth provider for token refresh
                    provider = get_zoho_provider(dc_config)
                    new_access_token = await provider.refresh_access_token(
                        refresh_token
                    )

                    if new_access_token:
                        logger.info(
                            f"Token refreshed successfully using {datacenter} OAuth endpoint, retrying request..."
                        )

                        # Update headers with new token
                        headers["Authorization"] = f"Zoho-oauthtoken {new_access_token}"

                        # Retry the request with new token
                        if method.upper() == "GET":
                            retry_response = await client.get(
                                url, headers=headers, params=params
                            )
                        elif method.upper() == "POST":
                            retry_response = await client.post(
                                url, headers=headers, json=data, params=params
                            )

                        # Log the retry response for debugging
                        logger.info(
                            f"RETRY Response from {datacenter}: Status {retry_response.status_code}"
                        )
                        logger.info(
                            f"RETRY Response Headers: {dict(retry_response.headers)}"
                        )
                        logger.info(f"RETRY Response Text: {retry_response.text}")

                        # Handle retry response
                        if retry_response.status_code in [200, 201, 202]:
                            try:
                                response_data = retry_response.json()
                                logger.info(
                                    f"RETRY Parsed JSON Response: {json.dumps(response_data, indent=2)}"
                                )
                                return {
                                    "success": True,
                                    "data": response_data,
                                    "status_code": retry_response.status_code,
                                    "token_refreshed": True,
                                    "new_access_token": new_access_token,
                                    "refresh_datacenter": datacenter,
                                }
                            except json.JSONDecodeError:
                                logger.warning(
                                    f"RETRY Failed to parse JSON response: {retry_response.text}"
                                )
                                return {
                                    "success": True,
                                    "data": {"raw_response": retry_response.text},
                                    "status_code": retry_response.status_code,
                                    "token_refreshed": True,
                                    "new_access_token": new_access_token,
                                    "refresh_datacenter": datacenter,
                                }
                        else:
                            # Retry also failed
                            try:
                                error_data = retry_response.json()
                            except json.JSONDecodeError:
                                error_data = {"raw_response": retry_response.text}

                            return {
                                "success": False,
                                "error": "AUTHENTICATION_FAILED_AFTER_REFRESH",
                                "message": f"Authentication failed even after token refresh using {datacenter}",
                                "status_code": retry_response.status_code,
                                "details": error_data,
                                "refresh_datacenter": datacenter,
                            }
                    else:
                        # Token refresh failed
                        return {
                            "success": False,
                            "error": "TOKEN_REFRESH_FAILED",
                            "message": f"Failed to refresh access token using {datacenter} OAuth endpoint",
                            "status_code": 401,
                            "refresh_datacenter": datacenter,
                        }

                # Handle other error responses
                else:
                    try:
                        error_data = response.json()
                    except json.JSONDecodeError:
                        error_data = {"raw_response": response.text}

                    if response.status_code == 401:
                        return {
                            "success": False,
                            "error": "AUTHENTICATION_FAILED",
                            "message": "Authentication failed - no refresh token available",
                            "status_code": 401,
                            "details": error_data,
                        }
                    elif response.status_code == 403:
                        return {
                            "success": False,
                            "error": "ACCESS_FORBIDDEN",
                            "message": "Insufficient permissions",
                            "status_code": 403,
                            "details": error_data,
                        }
                    elif response.status_code == 404:
                        return {
                            "success": False,
                            "error": "RESOURCE_NOT_FOUND",
                            "message": "Resource not found",
                            "status_code": 404,
                            "details": error_data,
                        }
                    elif response.status_code == 429:
                        return {
                            "success": False,
                            "error": "RATE_LIMIT_EXCEEDED",
                            "message": "API rate limit exceeded",
                            "status_code": 429,
                            "details": error_data,
                        }
                    else:
                        return {
                            "success": False,
                            "error": f"HTTP_{response.status_code}",
                            "message": f"API request failed: {response.reason_phrase}",
                            "status_code": response.status_code,
                            "details": error_data,
                        }

            except httpx.TimeoutException:
                logger.warning(f"Request timeout for {method} {url}")
                return {
                    "success": False,
                    "error": "REQUEST_TIMEOUT",
                    "message": "Request timed out",
                }
            except httpx.RequestError as e:
                logger.error(f"Request error for {method} {url}: {e}")
                return {
                    "success": False,
                    "error": "REQUEST_ERROR",
                    "message": f"Request failed: {str(e)}",
                }

    async def _try_datacenters(
        self,
        method: str,
        endpoint: str,
        access_token: str,
        refresh_token: Optional[str] = None,
        data: Optional[Dict] = None,
        params: Optional[Dict] = None,
    ) -> Dict[str, Any]:
        """Try datacenters in priority order, stopping on first success"""

        prioritized_dcs = self._get_prioritized_datacenters()
        logger.info(f"Trying {len(prioritized_dcs)} datacenters in priority order")

        for dc in prioritized_dcs:
            logger.debug(f"Trying datacenter: {dc}")

            try:
                url = self._get_datacenter_url(dc, endpoint)
                result = await self._make_request_with_refresh(
                    method, url, dc, access_token, refresh_token, data, params
                )

                # Add datacenter info to result
                result["_datacenter"] = dc

                # Return immediately on success
                if result.get("success", False):
                    logger.info(
                        f"SUCCESS from {dc} - stopping other datacenter attempts"
                    )
                    return result

                # Log failure and continue to next datacenter
                logger.warning(
                    f"Failed from {dc}: {result.get('error', 'Unknown error')}"
                )

            except Exception as e:
                logger.error(f"Exception from {dc}: {e}")
                continue

        # All datacenters failed
        logger.error("All datacenters failed")
        return {
            "success": False,
            "error": "ALL_DATACENTERS_FAILED",
            "message": f"All {len(prioritized_dcs)} datacenters failed",
        }

    # Public API methods
    async def create_record(
        self,
        module: str,
        record_data: Dict[str, Any],
        access_token: str,
        refresh_token: str = None,
    ) -> Dict[str, Any]:
        """Create a record using multi-datacenter approach with datacenter-specific token refresh"""
        endpoint = f"/{module}"
        return await self._try_datacenters(
            "POST",
            endpoint,
            access_token,
            refresh_token,
            data={"data": [record_data]},
        )

    async def search_by_phone(
        self,
        module: str,
        phone: str,
        access_token: str,
        refresh_token: str = None,
    ) -> Dict[str, Any]:
        """Search records by phone using Zoho's dedicated phone search endpoint with datacenter-specific token refresh"""
        endpoint = f"/{module}/search"
        params = {"phone": phone}
        return await self._try_datacenters(
            "GET",
            endpoint,
            access_token,
            refresh_token,
            params=params,
        )
