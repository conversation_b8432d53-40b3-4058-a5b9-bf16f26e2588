#!/usr/bin/env python3
"""
Zoho OAuth provider for token refresh and credential management
Similar to Google Calendar MCP server implementation.
"""

import logging
import httpx
from typing import Optional, Dict, Any
from urllib.parse import urlencode

from .config import ZohoConfig

logger = logging.getLogger(__name__)


class ZohoProvider:
    """Zoho API provider for OAuth management and token refresh."""

    def __init__(self, config: ZohoConfig):
        """
        Initialize ZohoProvider with configuration.

        Args:
            config: ZohoConfig instance with OAuth credentials
        """
        self.config = config
        self.oauth_token_url = config.oauth_token_url

        # Log configuration status for debugging
        logger.info(f"ZohoProvider initialized with environment: {config.environment}")
        logger.debug(f"OAuth URL: {self.oauth_token_url}")
        logger.info(f"Client ID configured: {'yes' if config.client_id else 'no'}")
        logger.info(
            f"Client Secret configured: {'yes' if config.client_secret else 'no'}"
        )
        if config.client_id:
            logger.info(f"Client ID starts with: {config.client_id[:10]}...")
        if config.client_secret:
            logger.info(f"Client Secret starts with: {config.client_secret[:10]}...")

        # Initialize request data for token refresh
        self.request_data = {
            "grant_type": "refresh_token",
            "client_id": config.client_id,
            "client_secret": config.client_secret,
        }

    async def refresh_access_token(
        self, refresh_token: str, user_id: str = None
    ) -> Optional[str]:
        """
        Refresh Zoho OAuth access token using refresh token.

        Args:
            refresh_token: The refresh token
            user_id: User ID for logging purposes (optional)

        Returns:
            New access token or None if failed
        """
        try:
            # Validate OAuth credentials
            if not self.config.client_id or not self.config.client_secret:
                logger.error(
                    f"Zoho OAuth credentials not configured - client_id: {'present' if self.config.client_id else 'missing'}, client_secret: {'present' if self.config.client_secret else 'missing'}"
                )
                return None

            request_data = self.request_data.copy()
            request_data["refresh_token"] = refresh_token

            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
            }

            logger.info(
                f"Refreshing Zoho access token for user: {user_id or 'unknown'}"
            )
            logger.debug(f"Token refresh URL: {self.oauth_token_url}")
            logger.debug(f"Request data: {dict(request_data)}")

            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.oauth_token_url,
                    data=urlencode(request_data),
                    headers=headers,
                    timeout=self.config.timeout,
                )

                # Don't raise for status immediately, handle Zoho-specific errors
                response_data = response.json()

                if response.status_code == 200:
                    new_access_token = response_data.get("access_token")
                    api_domain = response_data.get("api_domain")
                    expires_in = response_data.get("expires_in")
                    token_type = response_data.get("token_type")

                    if new_access_token:
                        logger.info(
                            f"Access token refreshed successfully. Expires in: {expires_in}s"
                        )
                        logger.debug(
                            f"API domain: {api_domain}, Token type: {token_type}"
                        )
                        return new_access_token
                    else:
                        logger.error("No access token in refresh response")
                        logger.error(f"Response data: {response_data}")
                        return None
                else:
                    # Handle Zoho-specific error responses
                    error_code = response_data.get("error")
                    error_description = response_data.get(
                        "error_description", "Unknown error"
                    )
                    logger.error(
                        f"Token refresh failed with status {response.status_code}"
                    )
                    logger.error(f"Error: {error_code} - {error_description}")
                    return None

        except httpx.RequestError as error:
            logger.error(
                f"HTTP error during token refresh: {str(error)} - user: {user_id or 'unknown'}"
            )
            return None
        except httpx.HTTPStatusError as error:
            logger.error(
                f"HTTP status error during token refresh: {error.response.status_code} - {error.response.text} - user: {user_id or 'unknown'}"
            )
            return None
        except ValueError as error:
            logger.error(
                f"JSON parsing error during token refresh: {str(error)} - user: {user_id or 'unknown'}"
            )
            return None
        except Exception as error:
            logger.error(
                f"Unexpected error during token refresh: {str(error)} - user: {user_id or 'unknown'}"
            )
            return None

    async def validate_credentials(
        self, access_token: str, refresh_token: str = None
    ) -> Dict[str, Any]:
        """
        Validate Zoho OAuth credentials.

        Args:
            access_token: Current access token
            refresh_token: Refresh token (optional)

        Returns:
            Dict with validation status and details
        """
        try:
            if not access_token:
                return {"success": False, "error": "Access token is required"}

            # Validate Zoho Client credentials first
            if not self.config.client_id or not self.config.client_secret:
                return {
                    "success": False,
                    "error": f"Zoho OAuth credentials not configured in environment - client_id: {'present' if self.config.client_id else 'missing'}, client_secret: {'present' if self.config.client_secret else 'missing'}",
                }

            # Test the access token by making a simple API call
            test_url = f"{self.config.api_base_url}/settings/modules"
            headers = {
                "Authorization": f"Zoho-oauthtoken {access_token}",
                "Content-Type": "application/json",
            }

            async with httpx.AsyncClient() as client:
                response = await client.get(
                    test_url, headers=headers, timeout=self.config.timeout
                )

                if response.status_code == 200:
                    return {
                        "success": True,
                        "message": "Credentials are valid and can access Zoho CRM API",
                        "has_refresh_token": bool(refresh_token),
                    }
                elif response.status_code == 401:
                    return {
                        "success": False,
                        "error": "Authentication failed: Invalid or expired access token",
                        "status_code": 401,
                    }
                elif response.status_code == 403:
                    return {
                        "success": False,
                        "error": "Access denied: Insufficient permissions",
                        "status_code": 403,
                    }
                else:
                    return {
                        "success": False,
                        "error": f"API validation failed with status {response.status_code}",
                        "status_code": response.status_code,
                    }

        except httpx.RequestError as error:
            error_str = str(error).lower()
            # Check if this is an authentication error
            if any(
                keyword in error_str
                for keyword in ["401", "403", "unauthorized", "forbidden"]
            ):
                return {
                    "success": False,
                    "error": f"Authentication error: {str(error)}",
                }
            return {
                "success": False,
                "error": f"Network error during validation: {str(error)}",
            }
        except Exception as error:
            return {"success": False, "error": f"Validation error: {str(error)}"}

    def get_oauth_info(self) -> Dict[str, Any]:
        """
        Get OAuth configuration information.

        Returns:
            Dict with OAuth configuration details
        """
        return {
            "oauth_token_url": self.oauth_token_url,
            "datacenter": self.config.environment,
            "environment_type": self.config.environment_type,
            "has_client_credentials": bool(
                self.config.client_id and self.config.client_secret
            ),
            "api_base_url": self.config.api_base_url,
        }


# Global instances for reuse by datacenter
zoho_providers: Dict[str, ZohoProvider] = {}


def get_zoho_provider(config: ZohoConfig) -> ZohoProvider:
    """Get or create a datacenter-specific Zoho provider instance"""
    global zoho_providers

    # Use datacenter as key to maintain separate providers
    datacenter_key = f"{config.environment}_{config.environment_type}"

    if datacenter_key not in zoho_providers:
        zoho_providers[datacenter_key] = ZohoProvider(config)
        logger.info(f"Created new ZohoProvider for datacenter: {datacenter_key}")
        logger.info(f"OAuth URL for {datacenter_key}: {config.oauth_token_url}")

    return zoho_providers[datacenter_key]
