#!/usr/bin/env python3
"""
Main MCP server implementation for Zoho CRM
Handles MCP protocol communication and tool routing.
"""

import json
import logging
import contextlib

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
import uvicorn
import asyncio
from typing import Dict, Any, List, Optional
from contextvars import ContextVar

import mcp.types as types
from mcp.server import Server
from mcp.server.streamable_http_manager import StreamableHTTPSessionManager
from starlette.applications import Starlette
from starlette.middleware import Middleware
from starlette.middleware.cors import CORSMiddleware
from starlette.routing import Route

from .constants.schema import *
from .constants.enum import Tools
from .config import Settings, get_settings
from .auth import (
    Auth<PERSON>and<PERSON>,
    AuthenticationError,
    set_access_token,
    set_refresh_token,
    get_refresh_token,
)
from .zoho_api import MultiDatacenterZohoClient

# Configure logging
logger = logging.getLogger(__name__)

# Initialize settings
settings = get_settings()

# Global multi-DC client (enabled by default)
multi_dc_client: Optional[MultiDatacenterZohoClient] = None

# Initialize MCP server
server = Server(settings.server.name)

# Context variables to store request headers for auth
access_token_context: ContextVar[str] = ContextVar("access_token")
refresh_token_context: ContextVar[str] = ContextVar("refresh_token")


def check_and_raise_on_error(result: dict) -> dict:
    """
    Check if the Zoho CRM operation failed and raise an exception if it did.
    This ensures the MCP framework sets isError: true for failed operations.
    """
    if not result.get("success", True):
        # Extract error message from the result
        error_msg = result.get("error", "Unknown error occurred")
        raise Exception(error_msg)
    return result


@server.list_tools()
async def handle_list_tools() -> list[types.Tool]:
    """
    List available tools.
    Each tool specifies its arguments using JSON Schema validation.
    """
    return [
        # CRM record management tools
        types.Tool(
            name=Tools.CREATE_RECORD,
            description="Create a new record in a Zoho CRM module. Required fields by module: Leads (Last_Name, Company), Contacts (Last_Name), Accounts (Account_Name), Deals (Deal_Name, Stage, Closing_Date)",
            inputSchema=CreateRecord.model_json_schema(),
        ),
        types.Tool(
            name=Tools.SEARCH_BY_PHONE,
            description="Search for Leads by phone number. Uses Zoho's dedicated phone search endpoint that automatically searches across all phone fields (Phone, Mobile, Home_Phone, Other_Phone, Fax).",
            inputSchema=SearchByPhone.model_json_schema(),
        ),
        # types.Tool(
        #     name=Tools.GET_RECORDS,
        #     description="Retrieve records from Zoho CRM with optional field filtering and pagination",
        #     inputSchema=GetRecords.model_json_schema(),
        # ),
        # types.Tool(
        #     name=Tools.SEARCH_RECORDS,
        #     description="Search records using Zoho-style criteria with advanced filtering",
        #     inputSchema=SearchRecords.model_json_schema(),
        # ),
        # # Server management tools
        # types.Tool(
        #     name=Tools.HEALTH_CHECK,
        #     description="Verify server health and Zoho connectivity",
        #     inputSchema=HealthCheck.model_json_schema(),
        # ),
        # types.Tool(
        #     name=Tools.GET_TOOL_INFO,
        #     description="Return detailed parameter specs for any tool",
        #     inputSchema=GetToolInfo.model_json_schema(),
        # ),
    ]


@server.call_tool()
async def handle_call_tool(name: str, arguments: dict | None) -> types.CallToolResult:
    """
    Handle tool execution requests.
    Tools can modify server state and notify clients of changes.
    """
    try:
        # Declare global variable at the top
        global multi_dc_client

        # Get tokens from context
        try:
            access_token = access_token_context.get()
            refresh_token = refresh_token_context.get()

            # Check if access token and refresh token are available
            if not access_token or not refresh_token:
                auth_handler = AuthHandler(settings.security)

                # Determine which tokens are missing
                missing_access = not access_token
                missing_refresh = not refresh_token

                if refresh_token and not access_token:
                    # Only access token is missing
                    error_msg = auth_handler.create_auth_error_response(
                        missing_access_token=True, missing_refresh_token=False
                    )
                elif access_token and not refresh_token:
                    # Only refresh token is missing
                    error_msg = auth_handler.create_auth_error_response(
                        missing_access_token=False, missing_refresh_token=True
                    )
                else:
                    # Both tokens are missing
                    error_msg = auth_handler.create_auth_error_response(
                        missing_access_token=True, missing_refresh_token=True
                    )

                raise Exception(json.dumps(error_msg))

        except LookupError:
            # No tokens in context at all
            auth_handler = AuthHandler(settings.security)
            error_msg = auth_handler.create_auth_error_response(
                missing_access_token=True, missing_refresh_token=True
            )
            raise Exception(json.dumps(error_msg))

        # Initialize multi-DC client if not already done
        if not multi_dc_client:
            multi_dc_client = MultiDatacenterZohoClient(settings.zoho)

        if name == Tools.CREATE_RECORD:
            result = await create_record_impl(
                module=arguments.get("module"),
                record_data=arguments.get("record_data", ""),
                multi_dc_client=multi_dc_client,
                access_token=access_token,
                refresh_token=refresh_token,
            )
            return [types.TextContent(type="text", text=json.dumps(result, indent=2))]

        elif name == Tools.SEARCH_BY_PHONE:
            result = await search_by_phone_impl(
                phone_number=arguments.get("phone_number"),
                multi_dc_client=multi_dc_client,
                access_token=access_token,
                refresh_token=refresh_token,
            )
            return [types.TextContent(type="text", text=json.dumps(result, indent=2))]

            # case Tools.GET_RECORDS:
            #     result = await get_records_impl(
            #         module=arguments.get("module"),
            #         fields=arguments.get("fields"),
            #         page=arguments.get("page", 1),
            #         per_page=arguments.get("per_page", 50),
            #         zoho_client=zoho_client,
            #         access_token=access_token,
            #     )
            #     return [
            #         types.TextContent(type="text", text=json.dumps(result, indent=2))
            #     ]

            # case Tools.SEARCH_RECORDS:
            #     result = await search_records_impl(
            #         module=arguments.get("module"),
            #         search_criteria=arguments.get("search_criteria"),
            #         page=arguments.get("page", 1),
            #         per_page=arguments.get("per_page", 200),
            #         zoho_client=zoho_client,
            #         access_token=access_token,
            #     )
            #     return [
            #         types.TextContent(type="text", text=json.dumps(result, indent=2))
            #     ]

            # case Tools.HEALTH_CHECK:
            #     result = await health_check_impl()
            #     return [
            #         types.TextContent(type="text", text=json.dumps(result, indent=2))
            #     ]

            # case Tools.GET_TOOL_INFO:
            #     result = await get_tool_info_impl(tool_name=arguments.get("tool_name"))
            #     return [
            #         types.TextContent(type="text", text=json.dumps(result, indent=2))
            #     ]

        else:
            raise Exception(f"Invalid tool: {name}")

    except Exception as error:
        logger.error(f"Error in tool {name}: {error}")
        # Let the MCP framework handle the error response with isError: true
        raise error


# Tool implementation functions
async def create_record_impl(
    module: str,
    record_data: str,
    multi_dc_client: MultiDatacenterZohoClient,
    access_token: str,
    refresh_token: str = None,
) -> Dict[str, Any]:
    """Implementation for creating Zoho CRM records"""
    try:
        if not module or not record_data:
            return {
                "success": False,
                "error": "MISSING_PARAMETERS",
                "message": "Both 'module' and 'record_data' are required",
            }

        # Parse the stringified JSON object
        try:
            parsed_record_data = json.loads(record_data)
            if not isinstance(parsed_record_data, dict):
                return {
                    "success": False,
                    "error": "INVALID_JSON_FORMAT",
                    "message": "record_data must be a JSON object (dictionary), not an array or primitive value",
                }
        except json.JSONDecodeError as e:
            return {
                "success": False,
                "error": "INVALID_JSON",
                "message": f"Failed to parse record_data as JSON: {str(e)}",
            }

        # Use multi-DC client
        result = await multi_dc_client.create_record(
            module, parsed_record_data, access_token, refresh_token
        )
        return result

    except Exception as e:
        error_msg = f"Error creating record: {str(e)}"
        logger.error(error_msg)
        return {"success": False, "error": "INTERNAL_ERROR", "message": error_msg}


async def search_by_phone_impl(
    phone_number: str,
    multi_dc_client: MultiDatacenterZohoClient,
    access_token: str,
    refresh_token: str = None,
) -> Dict[str, Any]:
    """Implementation for searching Leads by phone number using Zoho's dedicated phone search endpoint"""
    try:
        if not phone_number or not phone_number.strip():
            return {
                "success": False,
                "error": "MISSING_PARAMETERS",
                "message": "phone_number is required",
            }

        # Clean the phone number for search
        cleaned_phone = phone_number.strip()

        # Search only Leads module
        result = await multi_dc_client.search_by_phone(
            module="Leads",
            phone=cleaned_phone,
            access_token=access_token,
            refresh_token=refresh_token,
        )

        # Process results
        if result and result.get("success", True):
            # Handle nested data structure from Zoho API
            if "data" in result and isinstance(result["data"], dict):
                # Response has nested structure: {"data": {"data": [...], "info": {...}}}
                nested_data = result["data"]
                records = nested_data.get("data", [])
                info = nested_data.get("info", {})
                actual_count = info.get("count", len(records))
            else:
                # Response has flat structure: {"data": [...]}
                records = result.get("data", [])
                actual_count = len(records)

            # Add module information to each record
            for record in records:
                if isinstance(record, dict):
                    record["_module"] = "Leads"

            return {
                "success": True,
                "phone_number_searched": phone_number,
                "total_matches": actual_count,
                "matches": result.get("data", records),  # Return the original structure
                "message": f"Found {actual_count} Leads matching phone number '{phone_number}'",
            }
        else:
            error_msg = (
                result.get("message", "Search failed")
                if result
                else "No response from API"
            )
            logger.warning(f"Search failed: {error_msg}")
            return {
                "success": False,
                "error": "SEARCH_FAILED",
                "message": error_msg,
            }

    except Exception as e:
        error_msg = f"Error searching by phone number: {str(e)}"
        logger.error(error_msg)
        return {"success": False, "error": "INTERNAL_ERROR", "message": error_msg}


# async def get_records_impl(
#     module: str,
#     fields: Optional[List[str]],
#     page: int,
#     per_page: int,
#     zoho_client: ZohoAPIClient,
#     access_token: str,
# ) -> Dict[str, Any]:
#     """Implementation for retrieving Zoho CRM records"""
#     try:
#         if not module:
#             return {
#                 "success": False,
#                 "error": "MISSING_PARAMETERS",
#                 "message": "'module' parameter is required",
#             }

#         result = await zoho_client.get_records(
#             module, access_token, fields, page, per_page
#         )
#         return result

#     except Exception as e:
#         error_msg = f"Error retrieving records: {str(e)}"
#         logger.error(error_msg)
#         return {"success": False, "error": "INTERNAL_ERROR", "message": error_msg}


# async def search_records_impl(
#     module: str,
#     search_criteria: str,
#     page: int,
#     per_page: int,
#     zoho_client: ZohoAPIClient,
#     access_token: str,
# ) -> Dict[str, Any]:
#     """Implementation for searching Zoho CRM records"""
#     try:
#         if not module or not search_criteria:
#             return {
#                 "success": False,
#                 "error": "MISSING_PARAMETERS",
#                 "message": "Both 'module' and 'search_criteria' are required",
#             }

#         result = await zoho_client.search_records(
#             module, search_criteria, access_token, page, per_page
#         )
#         return result

#     except Exception as e:
#         error_msg = f"Error searching records: {str(e)}"
#         logger.error(error_msg)
#         return {"success": False, "error": "INTERNAL_ERROR", "message": error_msg}


# async def health_check_impl() -> Dict[str, Any]:
#     """Implementation for health check"""
#     try:
#         zoho_client = ZohoAPIClient(settings.zoho)
#         health_info = zoho_client.get_health_info()
#         result = {
#             "status": "healthy",
#             "service": settings.server.name,
#             "version": settings.server.version,
#             "server_url": f"http://{settings.server.host}:{settings.server.port}",
#             "zoho_api": health_info,
#             "authentication": {
#                 "required": settings.security.require_auth,
#                 "header": settings.security.auth_header_name,
#                 "scheme": settings.security.auth_scheme,
#             },
#         }
#         return result

#     except Exception as e:
#         error_msg = f"Error in health check: {str(e)}"
#         logger.error(error_msg)
#         return {
#             "success": False,
#             "error": "INTERNAL_ERROR",
#             "message": error_msg,
#         }


# async def get_tool_info_impl(tool_name: Optional[str]) -> Dict[str, Any]:
#     """Implementation for getting tool information"""
#     try:
#         # Define detailed tool information
#         tool_info = {
#             "create_record": {
#                 "name": "create_record",
#                 "description": "Create a new record in Zoho CRM module",
#                 "required_parameters": ["module", "record_data"],
#                 "optional_parameters": [],
#                 "module_requirements": {
#                     "Leads": {
#                         "required_fields": ["Last_Name", "Company"],
#                         "common_optional": [
#                             "Email",
#                             "Phone",
#                             "Mobile",
#                             "Lead_Source",
#                             "Website",
#                             "Industry",
#                             "Description",
#                         ],
#                     },
#                     "Contacts": {
#                         "required_fields": ["Last_Name"],
#                         "common_optional": [
#                             "First_Name",
#                             "Email",
#                             "Phone",
#                             "Mobile",
#                             "Account_Name",
#                             "Title",
#                             "Department",
#                         ],
#                     },
#                     "Accounts": {
#                         "required_fields": ["Account_Name"],
#                         "common_optional": [
#                             "Website",
#                             "Industry",
#                             "Annual_Revenue",
#                             "Phone",
#                             "Billing_Street",
#                             "Billing_City",
#                         ],
#                     },
#                     "Deals": {
#                         "required_fields": ["Deal_Name", "Stage", "Closing_Date"],
#                         "common_optional": [
#                             "Amount",
#                             "Account_Name",
#                             "Contact_Name",
#                             "Lead_Source",
#                             "Description",
#                         ],
#                     },
#                 },
#                 "example": {
#                     "module": "Leads",
#                     "record_data": "{\"Last_Name\": \"Smith\", \"Company\": \"Acme Corp\", \"Email\": \"<EMAIL>\", \"Phone\": \"******-0123\", \"Lead_Source\": \"Website\"}",
#                 },
#             },
#             "get_records": {
#                 "name": "get_records",
#                 "description": "Retrieve records from Zoho CRM with pagination and field selection",
#                 "required_parameters": ["module"],
#                 "optional_parameters": ["fields", "page", "per_page"],
#                 "parameter_details": {
#                     "module": "CRM module name (Leads, Contacts, Accounts, Deals, Tasks, Events, Calls)",
#                     "fields": "Array of field names to retrieve (if not provided, returns all fields)",
#                     "page": "Page number (default: 1, min: 1)",
#                     "per_page": "Records per page (default: 50, max: 200)",
#                 },
#                 "example": {
#                     "module": "Leads",
#                     "fields": [
#                         "Last_Name",
#                         "Company",
#                         "Email",
#                         "Phone",
#                         "Created_Time",
#                     ],
#                     "page": 1,
#                     "per_page": 50,
#                 },
#             },
#             "search_records": {
#                 "name": "search_records",
#                 "description": "Search records using advanced criteria",
#                 "required_parameters": ["module", "search_criteria"],
#                 "optional_parameters": ["page", "per_page"],
#                 "criteria_format": {
#                     "single": "(Field_Name:operator:value)",
#                     "multiple": "((Field1:equals:value1)and(Field2:contains:value2))",
#                     "date_example": "(Created_Time:greater_than:2024-01-01T00:00:00Z)",
#                 },
#                 "example": {
#                     "module": "Leads",
#                     "search_criteria": "((Last_Name:contains:Smith)and(Lead_Source:equals:Website))",
#                     "page": 1,
#                     "per_page": 100,
#                 },
#             },
#             "health_check": {
#                 "name": "health_check",
#                 "description": "Check server health and configuration",
#                 "required_parameters": [],
#                 "optional_parameters": [],
#                 "returns": [
#                     "status",
#                     "service",
#                     "version",
#                     "server_url",
#                     "zoho_api",
#                     "authentication",
#                 ],
#                 "example": {},
#             },
#         }

#         if tool_name:
#             if tool_name in tool_info:
#                 result = {"tool_info": tool_info[tool_name], "success": True}
#             else:
#                 result = {
#                     "error": f"Tool '{tool_name}' not found",
#                     "available_tools": list(tool_info.keys()),
#                     "success": False,
#                 }
#         else:
#             result = {
#                 "all_tools": tool_info,
#                 "summary": {
#                     "total_tools": len(tool_info),
#                     "tool_names": list(tool_info.keys()),
#                 },
#                 "success": True,
#             }

#         return result

#     except Exception as e:
#         error_msg = f"Error in get_tool_info: {str(e)}"
#         logger.error(error_msg)
#         return {
#             "success": False,
#             "error": "INTERNAL_ERROR",
#             "message": error_msg,
#         }


# HTTP Session Manager and Server Setup
async def create_app():
    """Create the Starlette application with MCP session manager"""

    # Create the session manager with the server
    try:
        session_manager = StreamableHTTPSessionManager(
            app=server,
            json_response=False,  # Use SSE format for responses
            stateless=False,  # Stateful mode for better user experience
        )
        logger.info("StreamableHTTPSessionManager initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize StreamableHTTPSessionManager: {e}")
        session_manager = None

    # Create a class for handling streamable HTTP connections
    class HandleStreamableHttp:
        def __init__(self, session_manager):
            self.session_manager = session_manager

        def _extract_headers(self, scope):
            """Extract authorization headers from request scope."""
            headers = dict(scope.get("headers", []))

            # Extract Authorization header (access token)
            access_token = None
            auth_header = headers.get(b"authorization")
            if auth_header:
                access_token = auth_header.decode("utf-8")
                access_token = access_token.replace("Bearer ", "")
                access_token_context.set(access_token)

            # Extract X-Refresh-Token header
            refresh_token = None
            refresh_header = headers.get(b"x-refresh-token")
            if refresh_header:
                refresh_token = refresh_header.decode("utf-8")
                refresh_token = refresh_token.replace("Bearer ", "")
                refresh_token_context.set(refresh_token)

            return access_token, refresh_token

        async def __call__(self, scope, receive, send):
            if self.session_manager is not None:
                try:
                    logger.debug("Handling Streamable HTTP connection...")

                    # Extract headers
                    access_token, refresh_token = self._extract_headers(scope)

                    # Set context variables (allow None values for missing headers)
                    if access_token:
                        access_token = access_token.replace("Bearer ", "")
                        access_token_context.set(access_token)
                        set_access_token(access_token)
                    if refresh_token:
                        refresh_token = refresh_token.replace("Bearer ", "")
                        refresh_token_context.set(refresh_token)
                        set_refresh_token(refresh_token)

                    await self.session_manager.handle_request(scope, receive, send)
                    logger.debug("Streamable HTTP connection closed")
                except Exception as e:
                    logger.error(f"Error handling Streamable HTTP request: {e}")
                    await send(
                        {
                            "type": "http.response.start",
                            "status": 500,
                            "headers": [(b"content-type", b"application/json")],
                        }
                    )
                    await send(
                        {
                            "type": "http.response.body",
                            "body": json.dumps(
                                {"error": f"Internal server error: {str(e)}"}
                            ).encode("utf-8"),
                        }
                    )
            else:
                # Return a 501 Not Implemented response if streamable HTTP is not available
                await send(
                    {
                        "type": "http.response.start",
                        "status": 501,
                        "headers": [(b"content-type", b"application/json")],
                    }
                )
                await send(
                    {
                        "type": "http.response.body",
                        "body": json.dumps(
                            {"error": "Streamable HTTP transport is not available"}
                        ).encode("utf-8"),
                    }
                )

    # Define routes
    routes = []

    # Add Streamable HTTP route if available
    if session_manager is not None:
        routes.append(
            Route(
                "/mcp", endpoint=HandleStreamableHttp(session_manager), methods=["POST"]
            )
        )

    middleware = [
        Middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_methods=["*"],
            allow_headers=["*"],
        )
    ]

    # Define lifespan for session manager
    @contextlib.asynccontextmanager
    async def lifespan(app):
        """Context manager for session manager."""
        if session_manager is not None:
            async with session_manager.run():
                logger.info("Application started with StreamableHTTP session manager!")
                try:
                    yield
                finally:
                    logger.info("Application shutting down...")
        else:
            # No session manager, just yield
            yield

    return Starlette(routes=routes, middleware=middleware, lifespan=lifespan)


async def start_server():
    """Start the server asynchronously."""
    app = await create_app()
    host = getattr(settings.server, "host", "0.0.0.0")
    port = getattr(settings.server, "port", 8000)

    logger.info(f"Starting Zoho CRM MCP server at {host}:{port}")

    # Use uvicorn's async API
    config = uvicorn.Config(app, host=host, port=port)
    server_instance = uvicorn.Server(config)
    await server_instance.serve()


def main():
    """Main entry point for the server"""
    while True:
        try:
            # Use asyncio.run to run the async start_server function
            asyncio.run(start_server())
        except KeyboardInterrupt:
            logger.info("Server stopped by user")
            break
        except Exception as e:
            logger.error(f"Server crashed with error: {e}")
            continue


if __name__ == "__main__":
    main()
