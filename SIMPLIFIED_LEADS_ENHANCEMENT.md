# Simplified Leads-Only Datacenter Switching Enhancement

## Problem & Solution

The previous implementation was getting too complex with datacenter switching logic applied to all modules. This simplified approach applies the enhanced datacenter switching **only to Leads** while keeping other modules (Contacts, Accounts, Deals) simple.

## Implementation Strategy

### 1. Conditional Logic in Server

**In `src/server.py` - `search_by_phone_impl()`:**
```python
# Use enhanced datacenter switching for Leads only, simple multi_call for others
if module == "Leads":
    result = await multi_dc_client.search_by_phone_with_dc_switching(
        module=module,
        phone=cleaned_phone,
        access_token=access_token,
        refresh_token=refresh_token,
        page=1,
        per_page=200,
    )
else:
    # Use simple multi_call for other modules
    result = await multi_dc_client.search_by_phone(
        module=module,
        phone=cleaned_phone,
        access_token=access_token,
        refresh_token=refresh_token,
        page=1,
        per_page=200,
    )
```

### 2. Two Different API Methods

**In `src/zoho_api.py`:**

#### Simple Method (for Contacts, Accounts, Deals):
```python
async def search_by_phone(self, ...):
    """Search records by phone using <PERSON><PERSON><PERSON>'s dedicated phone search endpoint (simple version)"""
    endpoint = f"/{module}/search?phone={phone}"
    return await self.multi_call("GET", endpoint, access_token, refresh_token=refresh_token)
```

#### Enhanced Method (for Leads only):
```python
async def search_by_phone_with_dc_switching(self, ...):
    """Search records by phone with enhanced datacenter switching (for Leads only)"""
    # Full datacenter switching logic with token refresh coordination
    # Reset token refresh state, prioritize successful datacenters, etc.
```

### 3. Simplified multi_call Method

**Removed complex logic from `multi_call()`:**
```python
async def multi_call(self, ...):
    """
    Simple multi-datacenter call without complex token refresh logic.
    Used for Contacts, Accounts, Deals (not Leads).
    """
    # Simple datacenter ordering: primary DC first, then others
    prioritized_dcs = [self.base_config.environment] + [
        dc for dc in self.datacenters if dc != self.base_config.environment
    ]
    # No token refresh state reset, no complex prioritization
```

## Complexity Reduction

### Before (Complex for All Modules):
- All 4 modules used complex datacenter switching
- Token refresh coordination for every search
- Datacenter prioritization for all modules
- Complex state management across all searches

### After (Simplified):
- **Leads (25%)**: Enhanced datacenter switching with token refresh
- **Others (75%)**: Simple multi-datacenter calls
- Reduced complexity for majority of searches
- Clear separation of concerns

## Expected Behavior

### Leads Search:
```
2025-07-24 21:25:06 - INFO - Searching Leads across 6 datacenters with DC switching
2025-07-24 21:25:06 - INFO - Starting centralized token refresh from EU
2025-07-24 21:25:07 - INFO - Token refreshed successfully from EU
2025-07-24 21:25:07 - INFO - Prioritizing EU datacenter (successful token refresh)
2025-07-24 21:25:07 - INFO - SUCCESS: Leads search from EU - stopping other datacenter attempts
```

### Other Modules (Contacts, Accounts, Deals):
```
2025-07-24 21:25:08 - INFO - Trying 6 datacenters in priority order
2025-07-24 21:25:08 - DEBUG - Trying datacenter: US
2025-07-24 21:25:08 - INFO - SUCCESS from US - stopping other datacenter attempts
```

## Benefits

### 1. **Reduced Complexity**
- 75% of searches use simple logic
- Easier to maintain and debug
- Lower risk of introducing bugs

### 2. **Focused Enhancement**
- Enhanced functionality where most needed (Leads)
- Leads typically have the highest volume and importance
- Other modules remain stable and simple

### 3. **Clear Separation**
- Easy to understand which module uses which approach
- Simple conditional logic in server
- No complex state sharing between modules

### 4. **Maintainability**
- Changes to enhanced logic only affect Leads
- Other modules remain unaffected
- Easier to test and validate

### 5. **Performance**
- Faster execution for Contacts, Accounts, Deals (no complex logic)
- Enhanced performance for Leads (datacenter switching)
- Optimal balance of simplicity and functionality

## Files Modified

1. **`src/server.py`**: Added conditional logic for Leads vs other modules
2. **`src/zoho_api.py`**: 
   - Added `search_by_phone_with_dc_switching()` for Leads
   - Simplified `search_by_phone()` for other modules
   - Simplified `multi_call()` method
   - Kept complex token refresh logic only for enhanced method

## Verification

Run the test to verify implementation:
```bash
python3 test_simplified_leads_enhancement.py
```

**Expected results:**
- ✅ All implementation checks pass
- ✅ Complexity reduction verified
- ✅ Clear separation between simple and enhanced methods

## Future Considerations

If other modules need enhanced datacenter switching:
1. Add them to the conditional logic in server
2. Use the existing `search_by_phone_with_dc_switching()` method
3. No changes needed to the underlying infrastructure

This approach provides a clean, maintainable solution that gives Leads the enhanced functionality while keeping the majority of the system simple and stable.
